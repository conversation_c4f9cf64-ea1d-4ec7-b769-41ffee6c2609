{"name": "bolt", "description": "StackBlitz AI Agent", "private": true, "license": "MIT", "packageManager": "pnpm@9.4.0", "sideEffects": false, "type": "module", "scripts": {"deploy": "npm run build && wrangler pages deploy", "build": "remix vite:build", "dev": "remix vite:dev", "test": "vitest --run", "test:watch": "vitest", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "lint:fix": "npm run lint -- --fix", "start": "bindings=$(./bindings.sh) && wrangler pages dev ./build/client $bindings", "typecheck": "tsc", "typegen": "wrangler types", "preview": "pnpm run build && pnpm run start"}, "engines": {"node": ">=18.18.0"}, "dependencies": {"@ai-sdk/anthropic": "^0.0.39", "@codemirror/autocomplete": "^6.17.0", "@codemirror/commands": "^6.6.0", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-css": "^6.2.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.2.5", "@codemirror/lang-python": "^6.1.6", "@codemirror/lang-sass": "^6.0.2", "@codemirror/lang-wast": "^6.0.2", "@codemirror/language": "^6.10.2", "@codemirror/search": "^6.5.6", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.28.4", "@iconify-json/ph": "^1.1.13", "@iconify-json/svg-spinners": "^1.1.2", "@lezer/highlight": "^1.2.0", "@nanostores/react": "^0.7.2", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@remix-run/cloudflare": "^2.10.2", "@remix-run/cloudflare-pages": "^2.10.2", "@remix-run/react": "^2.10.2", "@uiw/codemirror-theme-vscode": "^4.23.0", "@unocss/reset": "^0.61.0", "@webcontainer/api": "1.3.0-internal.10", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "ai": "^3.3.4", "date-fns": "^3.6.0", "diff": "^5.2.0", "framer-motion": "^11.2.12", "isbot": "^4.1.0", "istextorbinary": "^9.5.0", "jose": "^5.6.3", "nanostores": "^0.10.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hotkeys-hook": "^4.5.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.0.20", "react-toastify": "^10.0.5", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "remix-island": "^0.2.0", "remix-utils": "^7.6.0", "shiki": "^1.9.1", "unist-util-visit": "^5.0.0"}, "devDependencies": {"@blitz/eslint-plugin": "0.1.0", "@cloudflare/workers-types": "^4.20240620.0", "@remix-run/dev": "^2.10.0", "@types/diff": "^5.2.1", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "fast-glob": "^3.3.2", "is-ci": "^3.0.1", "node-fetch": "^3.3.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "unified": "^11.0.5", "unocss": "^0.61.3", "vite": "^5.3.1", "vite-plugin-node-polyfills": "^0.22.0", "vite-plugin-optimize-css-modules": "^1.1.0", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.0.1", "wrangler": "^3.63.2", "zod": "^3.23.8"}, "resolutions": {"@typescript-eslint/utils": "^8.0.0-alpha.30"}}